import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '파트너 로그인 | Shallwe',
  description:
    'Shallwe 파트너 로그인 페이지입니다. 파트너 계정으로 로그인하여 스튜디오를 관리하세요.',
  keywords: ['파트너', '로그인', 'Shallwe', '스튜디오'],
  openGraph: {
    title: '파트너 로그인 | Shallwe',
    description: 'Shallwe 파트너 로그인 페이지입니다.',
    type: 'website',
  },
};

export default function PartnerLoginLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className='flex min-h-screen flex-col bg-white'>
      {/* 메인 콘텐츠 */}
      <main className='flex flex-1 px-5'>{children}</main>

      {/* TODO: check footer content */}
      {/* 푸터 */}
      {/* <footer className='border-t border-gray-200 bg-white py-6'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='text-center text-sm text-gray-500'>
            <p>© 2024 Shallwe. All rights reserved.</p>
            <div className='mt-2 space-x-4'>
              <a
                href='/terms'
                className='transition-colors hover:text-gray-700'
              >
                이용약관
              </a>
              <span>|</span>
              <a
                href='/privacy'
                className='transition-colors hover:text-gray-700'
              >
                개인정보처리방침
              </a>
              <span>|</span>
              <a
                href='/support'
                className='transition-colors hover:text-gray-700'
              >
                고객센터
              </a>
            </div>
          </div>
        </div>
      </footer> */}
    </div>
  );
}
