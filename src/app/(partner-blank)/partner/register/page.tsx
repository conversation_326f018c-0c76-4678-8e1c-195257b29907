'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { PartnerSignupForm } from '@/components/partner/signup/PartnerSignupForm';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Toaster } from '@/components/ui/sonner';
import type { PartnerSignupFormData } from '@/schemas/partner';
import type { PartnerRegisterResponse } from '@/types/partner';

export default function PartnerRegisterPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleSubmit = async (data: PartnerSignupFormData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/partner/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result: PartnerRegisterResponse = await response.json();

      if (!response.ok) {
        // 409 Conflict - 이메일 중복 또는 파트너 중복
        if (response.status === 409) {
          toast.error('이미 등록된 이메일 주소입니다', {
            description: '다른 이메일로 다시 시도해주세요.'
          });
        }
        // 400 Bad Request - 입력 오류
        else if (response.status === 400) {
          toast.error('입력 정보를 확인해주세요', {
            description: result.message || '올바른 정보를 입력해주세요.'
          });
        }
        // 기타 오류
        else {
          toast.error('회원가입 중 오류가 발생했습니다', {
            description: result.message || '잠시 후 다시 시도해주세요.'
          });
        }
        
        setError((!result.success ? result.error : undefined) || result.message || '회원가입에 실패했습니다.');
        return;
      }

      // 성공 시 성공 메시지와 함께 로그인 페이지로 이동
      toast.success('회원 가입이 완료되었습니다.', {
        description: '로그인 페이지로 이동합니다.'
      });
      
      router.push('/partner/login');
    } catch (err) {
      console.error('회원가입 오류:', err);
      const errorMessage = err instanceof Error ? err.message : '회원가입 중 오류가 발생했습니다.';
      
      toast.error('네트워크 오류가 발생했습니다', {
        description: '인터넷 연결을 확인하고 다시 시도해주세요.'
      });
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-6">
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <PartnerSignupForm 
            onSubmit={handleSubmit}
            loading={loading}
            className="bg-gray-50"
          />
          
          <div className="text-center">
            <p className="text-sm text-gray-600">
              이미 계정이 있으신가요?{' '}
              <a href="/partner/login" className="text-purple-600 hover:text-purple-700 font-medium">
                로그인하기
              </a>
            </p>
          </div>
        </div>
      </div>
      <Toaster />
    </>
  );
}