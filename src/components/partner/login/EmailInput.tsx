'use client';

import { forwardRef } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface EmailInputProps {
  value: string;
  onChange: (value: string) => void;
  error?: string;
  disabled?: boolean;
  placeholder?: string;
  label?: string;
  id?: string;
  required?: boolean;
  className?: string;
}

export const EmailInput = forwardRef<HTMLInputElement, EmailInputProps>(
  (
    {
      value,
      onChange,
      error,
      placeholder = '<EMAIL>',
      label = '이메일',
      id = 'email',
      disabled = false,
      required = false,
      className,
      ...props
    },
    ref
  ) => {
    const hasError = !!error;
    const isValid =
      value && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) && !hasError;

    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <Label
            htmlFor={id}
            className={cn(
              'text-sm font-medium text-gray-700',
              hasError && 'text-red-600'
            )}
          >
            {label}
            {/* {required && <span className='ml-1 text-red-500'>*</span>} */}
          </Label>
        )}

        <div className='relative'>
          {/* 이메일 아이콘 */}
          <div className='pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3'>
            <svg
              className='h-5 w-5 text-gray-400'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'
              />
            </svg>
          </div>

          <Input
            ref={ref}
            id={id}
            type='email'
            value={value}
            onChange={e => onChange(e.target.value)}
            placeholder={placeholder}
            disabled={disabled}
            required={required}
            className={cn(
              'border-gray-300 pr-10 pl-10 transition-colors placeholder:text-gray-400 focus:border-purple-500 focus:ring-purple-500',
              hasError && 'border-red-500 focus:ring-red-500',
              isValid && 'border-green-500 focus:ring-green-500'
            )}
            aria-label={label}
            aria-required={required}
            aria-invalid={hasError}
            aria-describedby={hasError ? `${id}-error` : undefined}
            {...props}
          />

          {/* 유효성 아이콘 */}
          {value && (
            <div className='absolute inset-y-0 right-0 flex items-center pr-3'>
              {isValid ? (
                <svg
                  className='h-5 w-5 text-green-500'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M5 13l4 4L19 7'
                  />
                </svg>
              ) : (
                hasError && (
                  <svg
                    className='h-5 w-5 text-red-500'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                      d='M6 18L18 6M6 6l12 12'
                    />
                  </svg>
                )
              )}
            </div>
          )}
        </div>

        {/* 에러 메시지 */}
        {error && (
          <p
            id={`${id}-error`}
            className='flex items-center gap-1 text-sm text-red-600'
          >
            <svg
              className='h-4 w-4 flex-shrink-0'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
              />
            </svg>
            {error}
          </p>
        )}
      </div>
    );
  }
);

EmailInput.displayName = 'EmailInput';

export default EmailInput;
